import os
import yaml
import logging
from pathlib import Path
import sys

logger = logging.getLogger(__name__)

def get_app_path() -> Path:
    """兼容打包/开发环境的应用路径获取（增强路径校验）"""
    if getattr(sys, "frozen", False):
        app_path = Path(sys.executable).parent
    else:
        app_path = Path(__file__).parent.parent
    # 确保路径存在
    if not app_path.exists():
        raise FileNotFoundError(f"应用路径不存在: {app_path}")
    return app_path

class ConfigLoader:
    _config_data: dict = None

    @classmethod
    def _ensure_loaded(cls):
        if cls._config_data is None:
            cls._load_config()

    @classmethod
    def _load_config(cls):
        """增强配置加载逻辑（添加配置校验和类型检查）"""
        config_path = Path(os.path.join('../config', "config.yaml"))
        default_config = {
            'llm_server': 'deepseek',
            'deepseek': {
                'base_url': 'https://api.deepseek.com/v1',
                'api_key': None,
                'model_name': 'deepseek-chat-7b'
            },
            'ollama': {
                'base_url': 'http://localhost:11434',
                'model_name': 'llama3'
            },
            'VECTORDB_CONFIG': {
                'path': 'chroma_db',
                'collection_name': 'afsim_docs'
            },
            'VECTOR_MODEL_NAME': 'models/paraphrase-multilingual-mpnet-base-v2',
            'SEGMENT_SIZE': 400,
            'MIN_LENGTH': 50,
            'FILTER_DISTANCE': 0.65,
            'FULL_DOC_THRESHOLD': 0.6,  # 调优阈值（原0.45→0.6）
            'TITLE_MATCH_THRESHOLD': 0.7,
            'PROCESS_STATUS_FILE': 'doc_processing_status.json',
            'DOC_MAP_PATH': 'doc_map',
            'DOCS_PATH': 'docs',
            'LOG_LEVEL': 'INFO',
            'ENABLE_TRANSLATION': False,  # 新增翻译开关
            'SYNONYM_EXPAND_LIMIT': 5 ,   # 新增同义词扩展限制
            'CLUSTER_MODEL': 'models/all-MiniLM-L6-v2',  # 新增：聚类模型
            'BATCH_SIZE': 50
        }

        try:
            # 加载用户配置（添加文件类型校验）
            user_config = {}
            if config_path.exists() and config_path.suffix == '.yaml':
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
            else:
                logger.warning(f"配置文件 {config_path} 不存在或非YAML格式，使用默认配置")

            # 合并配置（深度合并避免覆盖子字典）
            cls._config_data = cls._deep_merge(default_config, user_config)

            # 校验关键配置项（添加必填项检查）
            required_keys = ['llm_server', 'VECTORDB_CONFIG', 'VECTOR_MODEL_NAME']
            for key in required_keys:
                if key not in cls._config_data:
                    raise ValueError(f"配置缺失必填项: {key}")

            # 处理路径（统一转换为绝对路径）
            base_path = get_app_path()
            cls._config_data["VECTORDB_CONFIG"]["path"] = str(base_path / "resources" / cls._config_data["VECTORDB_CONFIG"]["path"])
            cls._config_data["VECTOR_MODEL_NAME"] = str(base_path / "resources" / cls._config_data["VECTOR_MODEL_NAME"])
            cls._config_data["PROCESS_STATUS_FILE"] = str(base_path / "resources" / cls._config_data["PROCESS_STATUS_FILE"])
            cls._config_data["DOCS_PATH"] = str(base_path / "resources" / cls._config_data["DOCS_PATH"])
            cls._config_data["CLUSTER_MODEL"] = str(get_app_path() / "resources" / cls._config_data["CLUSTER_MODEL"])
            cls._config_data["SYNONYM_MODEL_PATH"] = str(get_app_path() / "resources" / cls._config_data["SYNONYM_MODEL_PATH"])

            # 设置日志（支持自定义日志格式）
            log_level = cls._config_data.get('LOG_LEVEL', 'INFO').upper()
            logging.basicConfig(
                level=getattr(logging, log_level, logging.INFO),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

            # 加载LLM配置（添加服务类型校验）
            server_name = cls._config_data['llm_server']
            if server_name not in ['deepseek', 'ollama']:
                raise ValueError(f"不支持的LLM服务: {server_name}")

            cls._get_llm_config(server_name)

            logger.info(f"配置加载成功，使用 {server_name} 服务")

        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}，使用默认配置")
            cls._config_data = default_config

    @staticmethod
    def _deep_merge(default: dict, user: dict) -> dict:
        """深度合并字典（处理嵌套配置）"""
        merged = default.copy()
        for key, value in user.items():
            if isinstance(value, dict) and isinstance(default.get(key), dict):
                merged[key] = ConfigLoader._deep_merge(default[key], value)
            else:
                merged[key] = value
        return merged

    @classmethod
    def _get_llm_config(cls, server_name: str) :
        """根据服务类型获取LLM配置（添加参数校验）"""
        if server_name == 'ollama':
            # 使用 Ollama 配置
            cls._config_data['DEEPSEEK_CONFIG'] = {
                'base_url': cls._config_data['ollama']['base_url'],
                'api_key': 'ollama',  # Ollama 不需要 API key
                'model_name': cls._config_data['ollama']['model_name']
            }
        else:
            # 使用 DeepSeek 配置
            deepseek_config = cls._config_data.get('deepseek', {})
            cls._config_data['DEEPSEEK_CONFIG'] = {
                'base_url': deepseek_config.get('base_url', 'https://api.deepseek.com/v1'),
                'api_key': deepseek_config.get('api_key_env', None),
                'model_name': deepseek_config.get('model_name', 'deepseek-chat')
            }

    def __getattr__(self, name):
        """通过属性访问配置项"""
        self._ensure_loaded()
        if name in ConfigLoader._config_data:
            return ConfigLoader._config_data[name]
        raise AttributeError(f"配置项 '{name}' 不存在")

    @classmethod
    def get_config(cls, name):
        """直接通过类访问配置项"""
        cls._ensure_loaded()
        if name in cls._config_data:
            return cls._config_data[name]
        raise AttributeError(f"配置项 '{name}' 不存在")

# 在类加载时初始化配置
ConfigLoader._load_config()

# 创建全局配置加载器实例
config = ConfigLoader()
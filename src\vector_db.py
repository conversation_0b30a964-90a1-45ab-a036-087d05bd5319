import chromadb
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
from config_loader import config, get_app_path
import os
import logging
import warnings
import sys
from sentence_transformers import SentenceTransformer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 忽略特定警告
warnings.filterwarnings("ignore", message=".*does not have a valid file.*")
warnings.filterwarnings("ignore", message=".*was never saved.*")


def initialize_vector_db():
    """
    初始化向量数据库，确保模型从本地加载
    包含详细的加载过程日志和错误处理
    """
    logger.info("开始初始化向量数据库...")

    try:
        # 确保向量数据库路径存在
        os.makedirs(config.VECTORDB_CONFIG["path"], exist_ok=True)
        logger.info(f"向量数据库路径已创建: {config.VECTORDB_CONFIG['path']}")

        # 创建持久化客户端
        client = chromadb.PersistentClient(path=config.VECTORDB_CONFIG["path"])
        logger.info("Chromadb 客户端创建成功")

        # 设置本地模型路径
        local_model_path = config.VECTOR_MODEL_NAME
        logger.info(f"准备从本地加载模型: {local_model_path}")

        # ================== 模型加载过程 ==================
        # 检查模型是否存在
        if not os.path.exists(local_model_path):
            logger.error(f"模型路径不存在: {local_model_path}")
            logger.error("请先下载模型到本地: sentence-transformers/paraphrase-multilingual-mpnet-base-v2")
            logger.error("下载命令: from sentence_transformers import SentenceTransformer; "
                         "model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-mpnet-base-v2'); "
                         "model.save('./models/paraphrase-multilingual-mpnet-base-v2')")
            raise FileNotFoundError(f"模型目录不存在: {local_model_path}")

        # 强制离线模式
        os.environ["TRANSFORMERS_OFFLINE"] = "1"
        logger.info("已设置 TRANSFORMERS_OFFLINE=1，强制离线模式")

        # 尝试预加载模型以验证完整性
        logger.info("开始预加载模型验证完整性...")
        try:
            test_model = SentenceTransformer(local_model_path, device="cpu")
            logger.info("模型预加载成功，完整性验证通过")
            # 释放测试模型资源
            del test_model
        except Exception as e:
            logger.error(f"模型预加载失败: {str(e)}")
            logger.error("可能原因: 模型文件损坏或不完整")
            logger.error("解决方案: 重新下载模型或检查文件完整性")
            raise

        # 创建嵌入函数
        logger.info("创建 SentenceTransformer 嵌入函数...")
        embedding_func = SentenceTransformerEmbeddingFunction(
            model_name=local_model_path,
            device="cpu"
        )
        logger.info("嵌入函数创建成功")
        # ================== 模型加载完成 ==================

        # 创建或获取集合
        logger.info(f"创建/获取集合: {config.VECTORDB_CONFIG['collection_name']}")
        collection = client.get_or_create_collection(
            name=config.VECTORDB_CONFIG["collection_name"],
            embedding_function=embedding_func,
            metadata={"hnsw:space": "cosine"}
        )
        logger.info("集合初始化完成")

        return collection

    except Exception as e:
        logger.exception("向量数据库初始化失败")
        # 提供具体的错误解决建议
        if "ConnectTimeout" in str(e):
            logger.error("检测到网络连接超时错误")
            logger.error("解决方案: 确保完全离线工作，检查模型本地路径")
            logger.error("或设置代理: export HTTP_PROXY=http://proxy.example.com:port")
        elif "FileNotFound" in str(e):
            logger.error("模型文件缺失错误")
            logger.error("解决方案: 下载模型到指定路径")
            logger.error("下载命令: python -c \"from sentence_transformers import SentenceTransformer; "
                         "model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-mpnet-base-v2'); "
                         "model.save('./models/paraphrase-multilingual-mpnet-base-v2')\"")
        else:
            logger.error(f"未知错误: {str(e)}")
            logger.error("解决方案: 检查chromadb和sentence-transformers版本兼容性")

        # 退出程序或抛出异常
        sys.exit(1)


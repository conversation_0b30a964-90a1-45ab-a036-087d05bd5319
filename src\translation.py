from openai import OpenAI
from config_loader import config
from logger import setup_logger

logger = setup_logger()
deepseek_config = config.DEEPSEEK_CONFIG
print(deepseek_config)
llm_client = OpenAI(
    base_url=deepseek_config["base_url"],
    api_key=deepseek_config["api_key"]
)


class TranslationClient:

    def to_english(self, text: str) -> str:
        """中文→英文翻译（增强错误处理）"""
        if not text.strip():
            return ""
        try:
            response = llm_client.chat.completions.create(
                model=deepseek_config["model_name"],
                messages=[{
                    "role": "user",
                    "content": f"将以下中文准确翻译成英文（保留技术术语）：{text}"
                }],
                temperature=0.1,
                max_tokens=1000
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"翻译失败: {str(e)}，返回原文")
            return text

# 初始化翻译客户端
translation_client = TranslationClient()
translate_to_english = translation_client.to_english
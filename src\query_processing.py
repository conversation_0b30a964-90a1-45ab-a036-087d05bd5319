import re
import numpy as np
from collections import defaultdict
from sentence_transformers import SentenceTransformer, util
from sklearn.cluster import AgglomerativeClustering
from config_loader import config
from vector_db import initialize_vector_db
from document_processing import get_full_document

# 初始化Sentence-BERT模型（轻量级预训练模型）
sbert_model = SentenceTransformer(config.CLUSTER_MODEL)
collection = initialize_vector_db()


def expand_query(question, domain_vocab=None):
    """基于语义相似性的智能查询扩展（支持领域词库）"""
    # if not domain_vocab:
    #     domain_vocab = config.DOMAIN_VOCAB  # 从配置文件加载领域词库（如["文档处理", "向量检索"]）
    domain_vocab = ["文档处理", "向量检索"]  # 从配置文件加载领域词库（如["文档处理", "向量检索"]）

    expanded = set(question.split())  # 初始词集合
    question_embedding = sbert_model.encode(question)  # 生成问题向量

    # 为每个问题词扩展相似词
    for word in question.split():
        word_embedding = sbert_model.encode(word)
        # 计算与领域词的相似度
        vocab_embeddings = sbert_model.encode(domain_vocab)
        cos_scores = util.cos_sim(word_embedding, vocab_embeddings)[0]
        # 取前3个相似度>0.6的词
        top_syns = sorted(zip(cos_scores, domain_vocab), key=lambda x: -x[0])[:3]
        for score, syn in top_syns:
            if score > 0.6 and syn not in expanded:
                expanded.add(syn)

    return " ".join(expanded)


def hybrid_retrieval(question, weights=None):
    """带权重的混合检索（关键词+向量+标题）"""
    # if not weights:
    #     weights = config.RETRIEVAL_WEIGHTS  # 从配置文件加载权重（如{"keyword":0.3, "vector":0.5, "title":0.2}）
    weights = {"keyword":0.3, "vector":0.5, "title":0.2} # 从配置文件加载权重（如{"keyword":0.3, "vector":0.5, "title":0.2}）

    # 执行三种检索
    keyword_results = collection.query(
        query_texts=[question], n_results=5, include=["documents", "distances", "metadatas"]
    )
    vector_results = collection.query(
        query_texts=[question], n_results=10, include=["documents", "distances", "metadatas"]
    )
    title_results = collection.query(
        query_texts=[question], n_results=3, where={"is_title": True}, include=["documents", "distances", "metadatas"]
    )

    # 合并结果并计算综合得分
    all_results = []
    seen_ids = set()

    # 标准化距离得分（距离越近，得分越高）
    def normalize_score(distance, max_distance=2.0):
        return 1 - (distance / max_distance)

    # 处理关键词检索结果
    for i in range(len(keyword_results['ids'][0])):
        doc_id = keyword_results['ids'][0][i]
        if doc_id not in seen_ids:
            score = normalize_score(keyword_results['distances'][0][i]) * weights['keyword']
            all_results.append({
                "id": doc_id,
                "document": keyword_results['documents'][0][i],
                "metadata": keyword_results['metadatas'][0][i],
                "score": score
            })
            seen_ids.add(doc_id)

    # 处理向量检索结果
    for i in range(len(vector_results['ids'][0])):
        doc_id = vector_results['ids'][0][i]
        if doc_id not in seen_ids:
            score = normalize_score(vector_results['distances'][0][i]) * weights['vector']
            all_results.append({
                "id": doc_id,
                "document": vector_results['documents'][0][i],
                "metadata": vector_results['metadatas'][0][i],
                "score": score
            })
            seen_ids.add(doc_id)

    # 处理标题检索结果
    for i in range(len(title_results['ids'][0])):
        doc_id = title_results['ids'][0][i]
        if doc_id not in seen_ids:
            score = normalize_score(title_results['distances'][0][i]) * weights['title']
            all_results.append({
                "id": doc_id,
                "document": title_results['documents'][0][i],
                "metadata": title_results['metadatas'][0][i],
                "score": score
            })
            seen_ids.add(doc_id)

    # 按综合得分降序排序
    all_results.sort(key=lambda x: -x['score'])
    return [(item['document'], 1 - item['score'], item['metadata']) for item in all_results]


def cluster_similar_documents(documents, question, min_similarity=0.7):
    """基于语义相似性的层次聚类（自动确定聚类数）"""
    if len(documents) == 0:
        return []

    # 生成文档嵌入
    embeddings = sbert_model.encode(documents)
    # 计算余弦距离矩阵（1-相似度）
    cos_dist_matrix = 1 - util.cos_sim(embeddings, embeddings).numpy()

    # 层次聚类（自动确定聚类数）
    clustering = AgglomerativeClustering(
        n_clusters=None,
        metric='precomputed',
        linkage='average',
        distance_threshold=1 - min_similarity  # 相似度≥min_similarity则合并
    )
    clustering.fit(cos_dist_matrix)

    # 按聚类分组
    clusters = defaultdict(list)
    for idx, label in enumerate(clustering.labels_):
        clusters[label].append(documents[idx])

    # 选择每个聚类的最优代表（关键词覆盖度+长度）
    question_keywords = set(question.lower().split())
    clustered_docs = []
    for cluster in clusters.values():
        # 计算关键词覆盖度
        cover_scores = [
            len(set(doc.lower().split()) & question_keywords) / len(doc.split())
            for doc in cluster
        ]
        # 选择覆盖度最高且长度适中的文档
        best_idx = np.argmax(cover_scores)
        clustered_docs.append(cluster[best_idx])

    return clustered_docs


def get_context(question):
    """获取与问题相关的上下文文档（动态阈值+聚类精简）"""
    expanded_question = expand_query(question)
    results = hybrid_retrieval(expanded_question)

    context_parts = []
    high_relevance_docs = set()
    title_matched_docs = set()

    # 动态阈值计算（查询越长，阈值越宽松）
    query_length = len(question.split())
    dynamic_title_threshold = config.TITLE_MATCH_THRESHOLD - min(query_length * 0.02, 0.2)
    dynamic_full_doc_threshold = config.FULL_DOC_THRESHOLD - min(query_length * 0.01, 0.1)

    # 第一步：处理标题匹配的文档（使用动态阈值）
    for doc, distance, metadata in results:
        if metadata.get('is_title', False) and distance < dynamic_title_threshold:
            doc_path = metadata.get('doc_path', '')
            if doc_path and doc_path not in title_matched_docs:
                title_matched_docs.add(doc_path)
                full_doc = get_full_document(doc_path)
                if full_doc:
                    doc_title = metadata.get('title', '未知标题')
                    context_parts.append(
                        f"完整文档内容（标题匹配: {doc_title}）:\n"
                        f"{full_doc}\n"
                        f"[相关度: {1 - distance:.2f}]"
                    )

    # 第二步：处理高相关度文档（使用动态阈值）
    for doc, distance, metadata in results:
        if metadata.get('is_title', False):  # 已处理过标题条目，跳过
            continue
        if distance < dynamic_full_doc_threshold:
            doc_path = metadata.get('doc_path', '')
            if doc_path and doc_path not in high_relevance_docs and doc_path not in title_matched_docs:
                high_relevance_docs.add(doc_path)
                full_doc = get_full_document(doc_path)
                if full_doc:
                    doc_title = metadata.get('title', '未知标题')
                    context_parts.append(
                        f"完整文档内容（来源: {doc_title}）:\n"
                        f"{full_doc}\n"
                        f"[相关度: {1 - distance:.2f}]"
                    )

    # 第三步：添加中等相关度片段（补充细节）
    for doc, distance, metadata in results:
        if metadata.get('is_title', False) or metadata.get('doc_path') in high_relevance_docs | title_matched_docs:
            continue
        # 仅保留中等相关度（阈值与高相关度衔接）
        if distance >= dynamic_full_doc_threshold and distance < 0.5:
            source = metadata.get('source', "未知来源")
            doc_title = metadata.get('title', '未知标题')
            section = metadata.get('section', '未知章节')
            relevance_score = 1 - distance
            context_parts.append(
                f"{doc}\n\n"
                f"[相关度: {relevance_score:.2f}]\n"
                f"[来源: {source} | 文档: {doc_title} | 章节: {section}]"
            )

    # 按相关度降序排序（提取相关度分数）
    def get_relevance_score(text):
        match = re.search(r"\[相关度: (\d+\.\d+)\]", text)
        return float(match.group(1)) if match else 0

    context_parts.sort(key=get_relevance_score, reverse=True)

    # 聚类精简（内容过多时）
    if len(context_parts) > 10:  # 从配置文件读取最大片段数（默认10）
        # 提取纯文本内容用于聚类
        plain_contents = [re.sub(r'\[.*?\]', '', part).strip() for part in context_parts]
        clustered_parts = cluster_similar_documents(plain_contents, question)
        # 恢复原格式（保留相关度和元数据）
        final_context = []
        for cluster_part in clustered_parts:
            # 找到原上下文中最接近聚类结果的片段（保留元数据）
            for original_part in context_parts:
                if cluster_part in original_part:
                    final_context.append(original_part)
                    break
        return "\n\n---\n\n".join(final_context[:10])

    return "\n\n---\n\n".join(context_parts[:10])



# afsim_agent_deepseek_full.py

import os
import re
import json
import markdown
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup
from langchain.agents import initialize_agent, Tool
from langchain.memory import ConversationBufferMemory
from langchain.prompts import PromptTemplate
from langchain.schema import Document
from langchain.output_parsers import StructuredOutputParser, ResponseSchema
from langchain_openai import ChatOpenAI
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import DirectoryLoader, TextLoader
import argparse

from config_loader import config
from document_processing import process_and_index_docs
from query_processing import get_context
from translation import translate_to_english
from vector_db import initialize_vector_db, logger

# -------- 初始化本地中文嵌入 --------
embeddings = HuggingFaceEmbeddings(model_name="BAAI/bge-large-zh")

# -------- 初始化 DeepSeek LLM --------
llm = ChatOpenAI(
    model="deepseek-chat",
    temperature=0,
    openai_api_key="sk-abb0a3daf36c49328d3b7f88f08f217f",
    openai_api_base="https://api.deepseek.com/v1"
)

def retrieve_afsim_params(question, top_k=5):
    # 安全过滤
    question = re.sub(r'[<>"\'&]', '', question)
    if len(question) > 500:
        return {"error": "问题过长"}, 400
    # 多语言查询支持（根据配置决定是否翻译）
    query_question = translate_to_english(question) if config.ENABLE_TRANSLATION else question
    # 使用英文问题获取上下文
    context = get_context(query_question)
    return context

# -------- 脚本生成工具 --------
def generate_afsim_script(user_input):
    parser = StructuredOutputParser.from_response_schemas([
        ResponseSchema(name="model_name", description="模型名称，如WZ-8"),
        ResponseSchema(name="model_type", description="模型类型，只能是 platform_type, sensors, weapons, comms")
    ])
    format_instructions = parser.get_format_instructions()
    prompt_extract = PromptTemplate(
        template="请从用户需求中提取模型名称和模型类型:\n{user_input}\n{format_instructions}",
        input_variables=["user_input"],
        partial_variables={"format_instructions": format_instructions}
    )
    parsed = parser.parse(llm(prompt_extract.format_prompt(user_input=user_input).to_messages()).content)
    model_type = parsed["model_type"]

    docs = retrieve_afsim_params(model_type, top_k=10)

    prompt_gen = f"""
你是AFSIM仿真脚本生成器，基于以下参数说明：
{docs}

请结合用户需求，生成符合AFSIM格式的{model_type}配置，字段用英文，且每行加中文注释。
用户需求：{user_input}
"""
    script = llm.predict(prompt_gen)
    return script

# -------- 脚本修改工具 --------
def modify_script(change_request, current_script):
    prompt = f"""
当前AFSIM脚本：
{current_script}

用户修改请求：
{change_request}

请直接修改脚本，保留中文注释。
"""
    new_script = llm.predict(prompt)
    return new_script

# -------- Agent 多轮内存 --------
memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

# -------- 工具列表 --------
tools = [
    Tool(name="检索参数", func=retrieve_afsim_params, description="根据模型类型检索AFSIM官方参数"),
    Tool(name="生成脚本", func=generate_afsim_script, description="根据用户需求生成AFSIM脚本"),
    Tool(name="修改脚本", func=lambda change: modify_script(change, memory.buffer if hasattr(memory, 'buffer') else ""), description="修改已有脚本"),
]

agent = initialize_agent(tools, llm, agent="chat-conversational-react-description", memory=memory, verbose=True)

# -------- 主函数 --------
def main():
    # 初始化向量数据库
    collection = initialize_vector_db()

    # 初始化文档索引
    if collection.count() == 0:
        logger.info("开始索引文档...")
        process_and_index_docs(collection)
        logger.info(f"向量数据库初始化完成，当前索引数量: {collection.count()}")
    else:
        logger.info(f"向量数据库已存在，当前索引数量: {collection.count()}")

    print("AFSIM DeepSeek Agent启动，输入exit退出")
    current_script = ""

    while True:
        user_input = input(">>> ").strip()
        if user_input.lower() == "exit":
            break
        if user_input.startswith("gen "):
            script = generate_afsim_script(user_input[4:])
            current_script = script
            print("\n=== 生成脚本 ===\n" + script)
        elif user_input.startswith("modify "):
            if not current_script:
                print("请先生成脚本，再修改")
                continue
            current_script = modify_script(user_input[7:], current_script)
            print("\n=== 修改后脚本 ===\n" + current_script)
        elif user_input == "show":
            print("\n=== 当前脚本 ===\n" + (current_script or "无脚本"))
        else:
            output = agent.run(user_input)
            print(output)

if __name__ == "__main__":
    main()

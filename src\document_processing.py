import re
import json
import markdown
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup
from config_loader import config
from logger import setup_logger

logger = setup_logger()


def clean_md_content(text):
    """清理Markdown内容（增强敏感信息过滤）"""
    # 移除图片、标题、加粗格式
    text = re.sub(r'!\[.*?\]\(.*?\)', '', text)
    text = re.sub(r'^#+\s.*$', '', text, flags=re.MULTILINE)
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)

    # 增强敏感信息过滤（手机号、邮箱、链接）
    text = re.sub(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', '[电话已移除]', text)
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[邮箱已移除]', text)
    text = re.sub(r'(https?://\S+|www\.\S+)', '[链接已移除]', text)
    return text


def extract_title_and_sections(md_content):
    """从Markdown内容中提取标题和主题段落（优化层级解析）"""
    html_content = markdown.markdown(md_content)
    soup = BeautifulSoup(html_content, 'html.parser')

    # 提取文档标题（优先h1，否则h2）
    title_tag = soup.find(['h1', 'h2'])
    doc_title = title_tag.get_text().strip() if title_tag else "未命名文档"

    # 按标题层级构建文档树（支持h1-h4）
    sections = []
    current_hierarchy = [{"title": doc_title, "content": "", "level": 1, "children": []}]

    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'ul', 'ol', 'pre']):
        if element.name.startswith('h'):
            level = int(element.name[1])
            title_text = element.get_text().strip()

            # 维护层级结构（确保子节点正确嵌套）
            while current_hierarchy and current_hierarchy[-1]['level'] >= level:
                sections.append(current_hierarchy.pop())

            new_node = {
                "title": title_text,
                "content": "",
                "level": level,
                "children": []
            }
            if current_hierarchy:
                current_hierarchy[-1]['children'].append(new_node)
            current_hierarchy.append(new_node)
        else:
            # 合并连续内容块（避免碎片化）
            if current_hierarchy:
                current_hierarchy[-1]['content'] += f" {element.get_text().strip()}"

    # 收尾剩余节点
    while current_hierarchy:
        sections.append(current_hierarchy.pop())

    return doc_title, sections


def load_processing_status():
    """加载文档处理状态（优化异常处理）"""
    try:
        if config.PROCESS_STATUS_FILE.exists():
            with open(config.PROCESS_STATUS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"加载处理状态失败: {str(e)}")
        return {}


def save_processing_status(status):
    """保存文档处理状态（优化原子性）"""
    try:
        with open(config.PROCESS_STATUS_FILE, 'w', encoding='utf-8') as f:
            json.dump(status, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"保存处理状态失败: {str(e)}")


def get_full_document(doc_path):
    """获取完整文档内容（增强健壮性）"""
    try:
        doc_map_path = Path(config.DOC_MAP_PATH) / f"{Path(doc_path).stem}.json"
        if not doc_map_path.exists():
            return None

        with open(doc_map_path, 'r', encoding='utf-8') as f:
            doc_map = json.load(f)

        # 按层级拼接完整内容（保留标题结构）
        full_content = f"# {doc_map['title']}\n\n"
        for section in doc_map['sections']:
            if section['level'] == 2:
                full_content += f"## {section['title']}\n"
            elif section['level'] == 3:
                full_content += f"### {section['title']}\n"
            full_content += f"{section['content']}\n\n"

        return full_content
    except Exception as e:
        logger.error(f"获取完整文档失败: {str(e)}")
        return None


def process_and_index_docs(collection):
    """批量处理并索引文档到向量数据库（优化分批策略）"""
    logger.info(f"开始批量处理文档，文档路径: {config.DOCS_PATH}")

    status = load_processing_status()
    md_files = list(Path(config.DOCS_PATH).rglob('*.md'))

    total_files = len(md_files)
    logger.info(f"找到 {total_files} 个Markdown文档")

    if total_files == 0:
        return

    Path(config.DOC_MAP_PATH).mkdir(exist_ok=True)
    batch_size = 50  # 从配置文件读取分批大小（默认50）
    indexed_count = 0
    skipped_count = 0
    error_count = 0

    for i in range(0, total_files, batch_size):
        batch_files = md_files[i:i + batch_size]
        documents, ids, metadatas = [], [], []

        for md_file in batch_files:
            file_path_str = str(md_file)
            if file_path_str in status and status[file_path_str].get('status') == 'success':
                last_modified = md_file.stat().st_mtime
                if status[file_path_str].get('last_modified') == last_modified:
                    skipped_count += 1
                    logger.info(f"跳过已处理文档: {md_file.name}")
                    continue

            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    md_content = f.read()

                last_modified = md_file.stat().st_mtime
                doc_title, sections = extract_title_and_sections(md_content)
                doc_map = {"path": file_path_str, "title": doc_title, "sections": []}

                # 索引标题条目
                title_entry_id = f"{md_file.stem}_title"
                documents.append(doc_title)
                ids.append(title_entry_id)
                metadatas.append({
                    "source": str(md_file.parent.name),
                    "title": doc_title,
                    "doc_path": file_path_str,
                    "is_title": True,
                    "is_segment": False
                })
                doc_map["sections"].append({
                    "id": title_entry_id,
                    "title": doc_title,
                    "content": doc_title,
                    "is_title": True,
                    "level": 1
                })

                # 索引章节内容（优化分段逻辑）
                for idx, section in enumerate(sections):
                    section_title = section["title"]
                    section_content = clean_md_content(section["content"])
                    section_level = section["level"]

                    # 按配置长度分段（避免过长片段）
                    if len(section_content) > config.SEGMENT_SIZE:
                        segments = [
                            section_content[j:j + config.SEGMENT_SIZE]
                            for j in range(0, len(section_content), config.SEGMENT_SIZE)
                        ]
                        for seg_idx, segment in enumerate(segments):
                            seg_id = f"{md_file.stem}_sec{idx}_seg{seg_idx}"
                            documents.append(segment)
                            ids.append(seg_id)
                            metadatas.append({
                                "source": str(md_file.parent.name),
                                "title": doc_title,
                                "section": section_title,
                                "doc_path": file_path_str,
                                "is_segment": True,
                                "is_title": False,
                                "level": section_level
                            })
                            doc_map["sections"].append({
                                "id": seg_id,
                                "title": section_title,
                                "content": segment,
                                "is_title": False,
                                "level": section_level
                            })
                    else:
                        seg_id = f"{md_file.stem}_sec{idx}"
                        documents.append(section_content)
                        ids.append(seg_id)
                        metadatas.append({
                            "source": str(md_file.parent.name),
                            "title": doc_title,
                            "section": section_title,
                            "doc_path": file_path_str,
                            "is_segment": False,
                            "is_title": False,
                            "level": section_level
                        })
                        doc_map["sections"].append({
                            "id": seg_id,
                            "title": section_title,
                            "content": section_content,
                            "is_title": False,
                            "level": section_level
                        })

                # 保存文档映射
                map_file = Path(config.DOC_MAP_PATH) / f"{md_file.stem}.json"
                with open(map_file, 'w', encoding='utf-8') as f:
                    json.dump(doc_map, f, ensure_ascii=False, indent=2)

                # 更新处理状态
                status[file_path_str] = {
                    "status": "success",
                    "last_modified": last_modified,
                    "processed_at": datetime.now().isoformat(),
                    "document_count": len(doc_map["sections"])
                }
                indexed_count += 1
                logger.info(f"已处理文档: {md_file.name} ({indexed_count}/{total_files})")

            except Exception as e:
                error_count += 1
                logger.error(f"处理文档 {md_file} 时出错: {str(e)}")
                status[file_path_str] = {
                    "status": "error",
                    "error_message": str(e),
                    "last_modified": md_file.stat().st_mtime,
                    "processed_at": datetime.now().isoformat()
                }

        # 批量添加到向量数据库（优化批量操作）
        if documents:
            collection.add(
                documents=documents,
                ids=ids,
                metadatas=metadatas
            )
        save_processing_status(status)

    logger.info(f"文档处理完成！成功: {indexed_count}, 跳过: {skipped_count}, 失败: {error_count}")
    save_processing_status(status)